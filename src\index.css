@import './styles/filter-components.css';
@import './styles/maplibre.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* MapLibre map styles are imported from maplibre.css */

/* Animation styles */

@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out forwards;
}

/* Z-Index Hierarchy for Console/UI Stacking
 * ========================================
 * 0-99:     Map base layers and content
 * 100-199:  Map controls (zoom, attribution, etc.)
 * 200-299:  Leaflet controls and overlays
 * 300-399:  Military control containers and panels
 * 400-499:  Analysis tools and advanced features
 * 500-599:  Modal dialogs and overlays
 * 600-699:  Tooltips and temporary displays
 * 700-799:  Error displays and notifications
 * 800-899:  Critical system alerts
 * 900-999:  Emergency/debug overlays
 */

/* Military-themed styling - Global */
@layer components {
  /* Military-themed base styles */
  .military-theme {
    @apply bg-military-black text-military-white font-military-body;
  }

  /* Military-themed header */
  .military-header {
    @apply bg-military-navy border-b border-military-border shadow-md;
  }

  /* Military-themed containers */
  .military-container {
    @apply bg-military-panel border border-military-border;
    clip-path: polygon(
      0 0,
      calc(100% - 10px) 0,
      100% 10px,
      100% 100%,
      10px 100%,
      0 calc(100% - 10px)
    );
  }

  /* Military-themed panels */
  .military-panel {
    @apply bg-military-panel border border-military-border;
    position: relative;
  }

  .military-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(to right, rgba(74, 93, 35, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(74, 93, 35, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
  }

  /* Military-themed headings */
  .military-heading {
    @apply font-military-heading text-military-white uppercase tracking-wider;
  }

  /* Military-themed section titles */
  .military-title {
    @apply font-military-stencil text-military-white uppercase tracking-wide;
  }

  /* Military-themed badges */
  .military-badge {
    @apply inline-flex items-center px-2 py-0.5 text-xs font-mono uppercase tracking-wider;
    clip-path: polygon(
      0 0,
      calc(100% - 5px) 0,
      100% 5px,
      100% 100%,
      5px 100%,
      0 calc(100% - 5px)
    );
  }

  /* Military-themed buttons */
  .military-btn {
    @apply bg-military-panel text-military-white border border-military-border
           transition-all duration-200 flex items-center justify-center;
    clip-path: polygon(
      0 0,
      calc(100% - 6px) 0,
      100% 6px,
      100% 100%,
      6px 100%,
      0 calc(100% - 6px)
    );
  }

  .military-btn:hover {
    @apply bg-military-darkgreen border-military-accent;
  }

  .military-btn.active {
    @apply bg-military-darkgreen border-military-accent;
    box-shadow: 0 0 5px rgba(94, 142, 62, 0.8);
  }

  /* Military-themed charts */
  .military-chart {
    @apply bg-military-panel border border-military-border rounded-none p-2;
  }

  /* Military-themed data displays */
  .military-data {
    @apply font-mono text-military-white bg-military-navy border border-military-border p-2;
  }

  /* Military-themed alerts */
  .military-alert {
    @apply bg-military-red text-military-white border border-military-border;
  }

  /* Military-themed warnings */
  .military-warning {
    @apply bg-military-amber text-military-black border border-military-border;
  }
}

/* Military-themed toolbar */
.military-toolbar {
  font-family: 'Roboto Mono', 'Courier New', monospace;
}

/* Military-themed control container */
.military-control-container {
  background-color: rgba(26, 26, 26, 0.95) !important;
  border: 1px solid #5E8E3E !important;
  color: #F5F5F5 !important;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  clip-path: polygon(
    0 0,
    calc(100% - 8px) 0,
    100% 8px,
    100% 100%,
    8px 100%,
    0 calc(100% - 8px)
  );
  z-index: 300 !important;
}

/* Fix for leaflet controls */
.leaflet-top,
.leaflet-bottom {
  z-index: 200 !important;
}

.leaflet-control-container .leaflet-top,
.leaflet-control-container .leaflet-bottom {
  z-index: 200 !important;
}

.military-control-container .header {
  background-color: #1C2541;
  border-bottom: 1px solid #5E8E3E;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.military-control-container .content {
  background-color: rgba(42, 58, 42, 0.95);
}

/* Military-themed buttons */
.military-button {
  background-color: #2A3A2A !important;
  color: #F5F5F5 !important;
  border: 1px solid #5E8E3E !important;
  transition: all 0.2s ease;
  padding: 4px !important;
  min-width: 28px !important;
  min-height: 28px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  clip-path: polygon(
    0 0,
    calc(100% - 4px) 0,
    100% 4px,
    100% 100%,
    4px 100%,
    0 calc(100% - 4px)
  );
  z-index: 310 !important;
}

.military-button:hover {
  background-color: #4A5D23 !important;
  color: #FFFFFF !important;
  border-color: #7FAE5E !important;
}

.military-button.active {
  background-color: #4A5D23 !important;
  color: #FFFFFF !important;
  box-shadow: 0 0 5px rgba(94, 142, 62, 0.8) !important;
}

/* Military-themed coordinates display */
.military-coordinates {
  background-color: rgba(35, 42, 35, 0.95) !important;
  color: #E0E0E0 !important;
  border: 1px solid #5E8E3E !important;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  padding: 3px 6px;
  border-radius: 4px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Military-themed marker icons */
.military-marker {
  filter: hue-rotate(100deg) saturate(0.8) brightness(0.9);
}

/* Tactical symbols - Stable positioning without movement */
.tactical-symbol-stable {
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
  /* Remove any transforms that could cause movement */
  transform: none !important;
  transition: none !important;
}

.tactical-symbol-stable:hover {
  /* Only change shadow on hover, no scaling or movement */
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8));
  cursor: pointer;
}

/* Simple marker styles for MapLibre compatibility */
.maplibre-marker-simple {
  transition: box-shadow 0.2s ease;
}

.maplibre-marker-simple:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.5) !important;
}

/* Legacy tactical symbols - keep for compatibility */
.tactical-symbol {
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
}

.tactical-symbol:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Response symbols */
.response-symbol {
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
}

.response-symbol:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Military-themed popup */
.military-popup .leaflet-popup-content-wrapper {
  background-color: rgba(47, 54, 47, 0.95);
  color: #E0E0E0;
  border: 1px solid #5E8E3E;
  font-family: 'Courier New', monospace;
}

.military-popup .leaflet-popup-tip {
  background-color: #3A4A3A;
  border: 1px solid #5E8E3E;
}

/* Range rings */
.range-ring {
  stroke: #5E8E3E;
  stroke-width: 2;
  stroke-dasharray: 5, 5;
  fill: none;
  opacity: 0.7;
}

/* Line of sight */
.line-of-sight {
  stroke: #FFD700;
  stroke-width: 2;
  stroke-dasharray: 10, 5;
  opacity: 0.8;
}

/* Military-themed drawing tools */
.leaflet-draw-toolbar.military a {
  background-color: #3A4A3A !important;
  color: #E0E0E0 !important;
}

.leaflet-draw-toolbar.military a:hover {
  background-color: #5E8E3E !important;
}

/* Military-themed tooltip */
.military-tooltip {
  background-color: rgba(47, 54, 47, 0.9);
  color: #E0E0E0;
  border: 1px solid #5E8E3E;
  padding: 4px 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  border-radius: 4px;
}

/* Military-themed card */
.military-card {
  background-color: #2A3A2A !important;
  border: 1px solid #5E8E3E !important;
  color: #F5F5F5 !important;
  font-family: 'Roboto Mono', 'Courier New', monospace !important;
  position: relative;
  clip-path: polygon(
    0 0,
    calc(100% - 12px) 0,
    100% 12px,
    100% 100%,
    12px 100%,
    0 calc(100% - 12px)
  );
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.military-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(74, 93, 35, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(74, 93, 35, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

.military-card h3 {
  color: #F5F5F5 !important;
  font-weight: bold !important;
  letter-spacing: 1px !important;
  font-size: 12px !important;
  text-transform: uppercase !important;
  font-family: 'Eurostile Bold Extended', 'Impact', 'Arial Black', sans-serif !important;
}

.military-card > div:last-child {
  position: relative;
  z-index: 1;
  flex-grow: 1 !important;
  overflow: hidden !important;
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Make the card header more compact and styled */
.military-card > div:first-child {
  padding: 6px 10px !important;
  min-height: 32px !important;
  background-color: #1C2541 !important;
  border-bottom: 1px solid #5E8E3E !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 320 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* Legend for tactical symbols */
.tactical-symbol-legend {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(47, 54, 47, 0.9);
  border: 1px solid #5E8E3E;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: #E0E0E0;
  z-index: 350;
  max-width: 200px;
  max-height: 300px;
  overflow-y: auto;
}